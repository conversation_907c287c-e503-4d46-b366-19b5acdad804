apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    deployment.strategy: rolling-update
    # Display name for human-readable identification
    app.display-name: "ai-spring-backend"
  annotations:
    deployment.kubernetes.io/revision: "1"
    argocd.argoproj.io/sync-wave: "2"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  progressDeadlineSeconds: 120
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ai-spring-backend
      app.kubernetes.io/name: ai-spring-backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-spring-backend
        app.kubernetes.io/name: ai-spring-backend
        app.kubernetes.io/component: springboot-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        app.kubernetes.io/part-of: ai-spring-backend
        environment: dev
        deployment.strategy: rolling-update
        # Display name for human-readable identification
        app.display-name: "ai-spring-backend"
    spec:
      securityContext:
        runAsNonRoot: false
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      containers:
      - name: ai-spring-backend
        image: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
        imagePullPolicy: Always
        securityContext:
          runAsNonRoot: false
          readOnlyRootFilesystem: false
          allowPrivilegeEscalation: true
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: STARTUP_PROBE_INITIAL_DELAY
          periodSeconds: STARTUP_PROBE_PERIOD
          timeoutSeconds: STARTUP_PROBE_TIMEOUT
          failureThreshold: STARTUP_PROBE_FAILURE_THRESHOLD
          successThreshold: STARTUP_PROBE_SUCCESS_THRESHOLD
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        env:
        - name: NODE_ENV
          value: dev
        - name: PORT
          value: "8080"
        envFrom:
        - configMapRef:
            name: ai-spring-backend-config
        - secretRef:
            name: ai-spring-backend-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
